import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect, test, describe, beforeAll, afterAll } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { setupTestEnvironment } from '../helpers/test-setup';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Wait for async operations to complete with smart polling
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Wait for a file to exist in Obsidian's vault
 */
async function waitForFileToExist(page: Page, filePath: string, timeout: number = 5000): Promise<boolean> {
  try {
    await page.waitForFunction(
      ({ path }) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        return !!file;
      },
      { path: filePath },
      { timeout }
    );
    return true;
  } catch (error) {
    console.log(`File ${filePath} did not appear within ${timeout}ms`);
    return false;
  }
}

/**
 * Wait for a condition to be true with Playwright's built-in retry mechanism
 */
async function waitForCondition(
  page: Page,
  condition: string,
  args: any = {},
  options: { timeout?: number; interval?: number } = {}
): Promise<any> {
  const { timeout = 5000, interval = 100 } = options;

  return page.waitForFunction(
    condition,
    args,
    { timeout, polling: interval }
  );
}

/**
 * Helper to access plugin sync metadata via Obsidian
 */
async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  return await page.evaluate(({ path }) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin || !plugin.syncMetadata) {
      throw new Error('Ghost sync plugin or syncMetadata not found');
    }

    // Get the TFile object - try both with and without leading slash
    let file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file && !path.startsWith('/')) {
      // Try with leading slash
      file = (window as any).app.vault.getAbstractFileByPath('/' + path);
    }
    if (!file && path.startsWith('/')) {
      // Try without leading slash
      file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
    }

    if (!file) {
      // List available files for debugging
      const allFiles = (window as any).app.vault.getAllLoadedFiles()
        .filter((f: any) => f.path.endsWith('.md'))
        .map((f: any) => f.path);
      throw new Error(`File not found: ${path}. Available files: ${allFiles.join(', ')}`);
    }

    return plugin.syncMetadata.getMetadata(file);
  }, { path: filePath });
}

/**
 * Helper to get changed_at timestamp for a file
 */
async function getChangedAt(page: Page, filePath: string): Promise<string | undefined> {
  const metadata = await getSyncMetadata(page, filePath);
  return metadata.changed_at;
}

/**
 * Helper to clean up test files using Obsidian's vault API
 */
async function cleanupTestFiles(page: Page): Promise<void> {
  // Retry cleanup up to 3 times to handle race conditions
  for (let attempt = 1; attempt <= 3; attempt++) {
    const result = await page.evaluate(async (attemptNum) => {
      const vault = (window as any).app.vault;

      // Get all files in the articles directory
      const allFiles = vault.getAllLoadedFiles()
        .filter((f: any) => f.path.startsWith('articles/') && f.path.endsWith('.md'));

      console.log(`Cleanup attempt ${attemptNum}: Found ${allFiles.length} files to delete`);

      // Delete each file
      let deletedCount = 0;
      for (const file of allFiles) {
        try {
          await vault.delete(file);
          deletedCount++;
          console.log(`Deleted file: ${file.path}`);
        } catch (error) {
          console.log(`Failed to delete file ${file.path}: ${error.message}`);
        }
      }

      return { deletedCount, totalFiles: allFiles.length };
    }, attempt);

    console.log(`Cleanup attempt ${attempt}: Deleted ${result.deletedCount}/${result.totalFiles} files`);

    // Wait for cleanup to complete
    await waitForAsyncOperation(1000);

    // Check if all files were deleted
    const remainingFiles = await page.evaluate(() => {
      const vault = (window as any).app.vault;
      return vault.getAllLoadedFiles()
        .filter((f: any) => f.path.startsWith('articles/') && f.path.endsWith('.md'))
        .map((f: any) => f.path);
    });

    if (remainingFiles.length === 0) {
      console.log(`✅ All files cleaned up successfully on attempt ${attempt}`);
      return; // Success!
    }

    console.log(`⚠️  Cleanup attempt ${attempt} incomplete. Remaining files: ${remainingFiles.join(', ')}`);

    if (attempt < 3) {
      await waitForAsyncOperation(1000); // Wait before retry
    }
  }

  console.log(`⚠️  Cleanup completed after 3 attempts, some files may remain`);
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  // Retry file creation up to 3 times
  for (let attempt = 1; attempt <= 3; attempt++) {
    const result = await page.evaluate(async ({ path, fileContent, attemptNum }) => {
      try {
        // Ensure the articles directory exists
        const articlesDir = 'articles';
        const articlesFolder = (window as any).app.vault.getAbstractFileByPath(articlesDir);
        if (!articlesFolder) {
          try {
            await (window as any).app.vault.createFolder(articlesDir);
          } catch (e) {
            // Folder might already exist, ignore error
          }
        }

        // Create the file using Obsidian's vault API
        await (window as any).app.vault.create(path, fileContent);
        return { success: true, message: `Created file: ${path} (attempt ${attemptNum})` };
      } catch (error) {
        // If file already exists, modify it instead
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        if (file) {
          await (window as any).app.vault.modify(file, fileContent);
          return { success: true, message: `Modified existing file: ${path} (attempt ${attemptNum})` };
        }
        return { success: false, error: error.message, attempt: attemptNum };
      }
    }, { path: filePath, fileContent: content, attemptNum: attempt });

    if (result.success) {
      console.log(result.message);

      // Wait for the file to be available in the vault using Playwright's waitForFunction
      const fileExists = await waitForFileToExist(page, filePath);
      if (fileExists) {
        return; // Success!
      }
    }

    console.log(`File creation attempt ${attempt} failed: ${result.error || 'File not found after creation'}`);

    if (attempt < 3) {
      await waitForAsyncOperation(1000); // Wait before retry
    }
  }

  // If all attempts failed, list available files for debugging
  const availableFiles = await page.evaluate(() => {
    const allFiles = (window as any).app.vault.getAllLoadedFiles()
      .filter((f: any) => f.path.endsWith('.md'))
      .map((f: any) => f.path);
    return allFiles;
  });

  throw new Error(`Failed to create test file ${filePath} after 3 attempts. Available files: ${availableFiles.join(', ')}`);
}

describe("Ghost Sync - changed_at Handling E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await waitForAsyncOperation(1000);

    // Setup test environment with plugin verification and UI reset
    await setupTestEnvironment(page);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clean up test files using Obsidian's vault API
    await cleanupTestFiles(page);

    // Also clear any existing files from the filesystem as backup
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }

    // Wait for cleanup operations to complete
    await waitForAsyncOperation(500);
  });

  test("should set changed_at when manually marking file as changed", async () => {
    const testTitle = "Test Changed At Post";
    const testSlug = "test-changed-at-post";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test file with frontmatter including a slug
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Featured Image: null
Newsletter: null
---

# ${testTitle}

This is test content for changed_at handling.`;

    await createTestFile(page, relativeFilePath, content);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForAsyncOperation(500);

    // Verify no initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    console.log(`Initial changed_at: ${initialChangedAt}`);

    // Manually mark the file as changed using the plugin's sync metadata
    const markResult = await page.evaluate(async (path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (!plugin || !plugin.syncMetadata) {
        return { success: false, error: 'Plugin or syncMetadata not found' };
      }

      // Get the TFile object - try both with and without leading slash
      let file = (window as any).app.vault.getAbstractFileByPath(path);
      if (!file && !path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath('/' + path);
      }
      if (!file && path.startsWith('/')) {
        file = (window as any).app.vault.getAbstractFileByPath(path.substring(1));
      }

      if (!file) {
        const allFiles = (window as any).app.vault.getAllLoadedFiles()
          .filter((f: any) => f.path.endsWith('.md'))
          .map((f: any) => f.path);
        return { success: false, error: `File not found: ${path}. Available: ${allFiles.join(', ')}` };
      }

      try {
        await plugin.syncMetadata.markAsChanged(file);
        return { success: true, filePath: file.path };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }, relativeFilePath);

    console.log('Mark as changed result:', markResult);
    expect(markResult.success).toBe(true);

    await waitForAsyncOperation(1000);

    // Check that changed_at was set
    const changedAt = await getChangedAt(page, relativeFilePath);

    expect(changedAt).toBeTruthy();
    expect(new Date(changedAt).getTime()).toBeGreaterThan(0);

    // If there was an initial timestamp, verify the new one is different
    if (initialChangedAt) {
      expect(changedAt).not.toBe(initialChangedAt);
    }

    console.log(`✅ changed_at set for file: ${changedAt}`);
  });

  test("should update changed_at timestamp when marked multiple times", async () => {
    const testSlug = "test-multiple-changes";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Multiple Changes"
Slug: "${testSlug}"
Status: "draft"
---

Initial content.`;

    await createTestFile(page, relativeFilePath, initialContent);

    // Give a moment for the file to be fully processed by Obsidian
    await waitForAsyncOperation(500);

    // Mark as changed first time
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    expect(initialChangedAt).toBeTruthy();

    // Wait a bit to ensure timestamp difference
    await waitForAsyncOperation(1000);

    // Mark as changed second time
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Check that changed_at was updated
    const updatedChangedAt = await getChangedAt(page, relativeFilePath);

    expect(updatedChangedAt).toBeTruthy();
    expect(updatedChangedAt).not.toBe(initialChangedAt);
    expect(new Date(updatedChangedAt).getTime()).toBeGreaterThan(new Date(initialChangedAt).getTime());

    console.log(`✅ changed_at updated: ${initialChangedAt} -> ${updatedChangedAt}`);
  });

  test("should persist changed_at in sync metadata storage", async () => {
    const testSlug = "test-persistence";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const content = `---
Title: "Test Persistence"
Slug: "${testSlug}"
Status: "draft"
---

Content for persistence test.`;

    await createTestFile(page, relativeFilePath, content);

    // Mark as changed
    await page.evaluate(({ path }) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, { path: relativeFilePath });

    await waitForAsyncOperation(500);

    // Get changed_at
    const changedAt = await getChangedAt(page, relativeFilePath);
    expect(changedAt).toBeTruthy();

    // Verify it's stored in plugin data (not frontmatter)
    const pluginData = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.loadData();
    });

    const syncMetadata = (await pluginData)?.['sync-metadata'];
    expect(syncMetadata).toBeTruthy();
    expect(syncMetadata[relativeFilePath]).toBeTruthy();
    expect(syncMetadata[relativeFilePath].changed_at).toBe(changedAt);

    console.log(`✅ changed_at persisted in sync metadata: ${changedAt}`);
  });
});
