import { defineConfig } from 'vitest/config';
import { svelte } from '@sveltejs/vite-plugin-svelte';

export default defineConfig({
  plugins: [
    svelte({
      compilerOptions: {
        compatibility: {
          componentApi: 4
        }
      },
      hot: false
    })
  ],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.ts', './e2e/setup.ts'],
    globalSetup: ['./e2e/global-setup.js'],
    include: ['tests/**/*.test.ts', 'tests/**/*.test.js', 'e2e/**/*.e2e.ts'],
    testTimeout: 120000, // 2 minutes for e2e tests
    hookTimeout: 30000   // 30 seconds for setup/teardown hooks
  },
  resolve: {
    alias: {
      'obsidian': new URL('./tests/__mocks__/obsidian.ts', import.meta.url).pathname
    },
    conditions: ['browser']
  },
  define: {
    global: 'globalThis'
  }
});
